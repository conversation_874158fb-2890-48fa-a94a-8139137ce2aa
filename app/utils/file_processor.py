"""
File processing utilities for handling multi-modal attachments in AutoGen agents.
Supports images, documents, and other file types for agent chat interactions.
"""

import base64
import logging
import ssl
from io import Bytes<PERSON>
from typing import List
from urllib.parse import urlparse

import aiohttp
import requests
from autogen_agentchat.messages import MultiModalMessage
from autogen_core import Image as AGImage
from PIL import Image

from ..schemas.kafka import MessageAttachment

logger = logging.getLogger(__name__)


class FileProcessor:
    """Handles processing of file attachments for multi-modal agent interactions"""

    # Supported image formats
    SUPPORTED_IMAGE_TYPES = {
        "image/jpeg",
        "image/jpg",
        "image/png",
        "image/gif",
        "image/bmp",
        "image/webp",
        "image/tiff",
    }

    # Supported document formats
    SUPPORTED_DOCUMENT_TYPES = {
        "application/pdf",
        "text/plain",
        "text/markdown",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "application/vnd.ms-excel",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    }

    def __init__(self):
        self.logger = logger

    def is_supported_file_type(self, file_type: str) -> bool:
        """Check if the file type is supported for processing"""
        return (
            file_type in self.SUPPORTED_IMAGE_TYPES
            or file_type in self.SUPPORTED_DOCUMENT_TYPES
        )

    def is_image_file(self, file_type: str) -> bool:
        """Check if the file is an image"""
        return file_type in self.SUPPORTED_IMAGE_TYPES

    def is_document_file(self, file_type: str) -> bool:
        """Check if the file is a document"""
        return file_type in self.SUPPORTED_DOCUMENT_TYPES

    def decode_base64_file(self, base64_data: str) -> bytes:
        """Decode base64 encoded file data"""
        try:
            # Remove data URL prefix if present (e.g., "data:image/jpeg;base64,")
            if "," in base64_data:
                base64_data = base64_data.split(",", 1)[1]

            return base64.b64decode(base64_data)
        except Exception as e:
            self.logger.error(f"Failed to decode base64 data: {e}")
            raise ValueError(f"Invalid base64 data: {e}")

    async def download_file_from_url(self, file_url: str) -> bytes:
        """Download file content from URL with proper SSL handling"""
        try:
            # Validate URL
            parsed_url = urlparse(file_url)
            if not parsed_url.scheme or not parsed_url.netloc:
                raise ValueError(f"Invalid URL: {file_url}")

            # Create SSL context with proper certificate verification
            ssl_context = ssl.create_default_context()

            # Configure timeout
            timeout = aiohttp.ClientTimeout(total=30, connect=10)

            # Set up headers
            headers = {
                "User-Agent": "Mozilla/5.0 (compatible; AutoGen-Agent/1.0)",
                "Accept": "*/*",
                "Accept-Encoding": "gzip, deflate",
                "Connection": "keep-alive",
            }

            # Create connector with SSL context
            connector = aiohttp.TCPConnector(ssl=ssl_context)

            # Download file with proper SSL configuration
            async with aiohttp.ClientSession(
                connector=connector, timeout=timeout, headers=headers
            ) as session:
                async with session.get(file_url) as response:
                    if response.status != 200:
                        raise ValueError(
                            f"Failed to download file: HTTP {response.status}"
                        )

                    content = await response.read()
                    self.logger.info(f"Downloaded {len(content)} bytes from {file_url}")
                    return content

        except ssl.SSLError as ssl_error:
            # Handle SSL errors specifically with fallback option
            self.logger.warning(
                f"SSL verification failed for {file_url}: {ssl_error}. "
                "Attempting download with SSL verification disabled."
            )
            try:
                # Fallback: disable SSL verification (for development only)
                ssl_context = ssl.create_default_context()
                ssl_context.check_hostname = False
                ssl_context.verify_mode = ssl.CERT_NONE

                timeout = aiohttp.ClientTimeout(total=30, connect=10)
                headers = {
                    "User-Agent": "Mozilla/5.0 (compatible; AutoGen-Agent/1.0)",
                    "Accept": "*/*",
                }

                connector = aiohttp.TCPConnector(ssl=ssl_context)

                async with aiohttp.ClientSession(
                    connector=connector, timeout=timeout, headers=headers
                ) as session:
                    async with session.get(file_url) as response:
                        if response.status != 200:
                            raise ValueError(
                                f"Failed to download file: HTTP {response.status}"
                            )

                        content = await response.read()
                        self.logger.info(
                            f"Downloaded {len(content)} bytes from {file_url} "
                            "(SSL verification disabled)"
                        )
                        return content

            except Exception as fallback_error:
                self.logger.error(
                    f"Fallback download also failed for {file_url}: "
                    f"{fallback_error}"
                )
                raise ValueError(
                    f"Failed to download file even with SSL verification "
                    f"disabled: {fallback_error}"
                )

        except Exception as e:
            self.logger.error(f"Failed to download file from {file_url}: {e}")
            raise ValueError(f"Failed to download file: {e}")

    async def get_file_content(self, attachment: MessageAttachment) -> bytes:
        """Get file content from either base64 data or URL"""
        if attachment.file_data:
            return self.decode_base64_file(attachment.file_data)
        elif attachment.file_url:
            return await self.download_file_from_url(attachment.file_url)
        else:
            raise ValueError("Either file_data or file_url must be provided")

    async def process_image_attachment(self, attachment: MessageAttachment) -> Image:
        """Process an image attachment and return an AutoGen Image object"""
        try:
            # Get image data from either base64 or URL
            image_bytes = await self.get_file_content(attachment)

            # Create PIL Image from bytes
            pil_image = Image.open(BytesIO(image_bytes))

            # Create AutoGen Image object
            autogen_image = AGImage(pil_image)

            source = (
                "base64 data" if attachment.file_data else f"URL: {attachment.file_url}"
            )
            self.logger.info(
                f"Successfully processed image: {attachment.file_name} from {source}"
            )
            return autogen_image

        except Exception as e:
            self.logger.error(f"Failed to process image {attachment.file_name}: {e}")
            raise ValueError(f"Failed to process image: {e}")

    async def process_document_attachment(self, attachment: MessageAttachment) -> str:
        """Process a document attachment and return text content"""
        try:
            # Get document data from either base64 or URL
            document_bytes = await self.get_file_content(attachment)

            # For now, handle text files directly
            if attachment.file_type in ["text/plain", "text/markdown"]:
                content = document_bytes.decode("utf-8")
                source = (
                    "base64 data"
                    if attachment.file_data
                    else f"URL: {attachment.file_url}"
                )
                self.logger.info(
                    f"Successfully processed text document: {attachment.file_name} from {source}"
                )
                return content

            # For other document types, return a placeholder
            # In a production system, you'd use libraries like PyPDF2, python-docx, etc.
            self.logger.warning(
                f"Document type {attachment.file_type} not fully supported yet"
            )
            return f"[Document: {attachment.file_name} ({attachment.file_type})]"

        except Exception as e:
            self.logger.error(f"Failed to process document {attachment.file_name}: {e}")
            raise ValueError(f"Failed to process document: {e}")

    async def create_multimodal_message(
        self,
        text_content: str,
        attachments: List[MessageAttachment],
        source: str = "user",
    ) -> MultiModalMessage:
        """
        Create a MultiModalMessage from text content and attachments

        Args:
            text_content: The text part of the message
            attachments: List of file attachments
            source: Source of the message (default: "user")

        Returns:
            MultiModalMessage object ready for AutoGen processing
        """
        try:
            # Start with text content
            content_parts = [text_content] if text_content.strip() else []

            # Process each attachment
            for attachment in attachments:
                if not self.is_supported_file_type(attachment.file_type):
                    self.logger.warning(
                        f"Unsupported file type: {attachment.file_type}"
                    )
                    continue

                if self.is_image_file(attachment.file_type):
                    # Process image and add to content
                    image = await self.process_image_attachment(attachment)
                    content_parts.append(image)

                elif self.is_document_file(attachment.file_type):
                    # Process document and add text content
                    doc_content = await self.process_document_attachment(attachment)
                    content_parts.append(
                        f"\n\n--- Content from {attachment.file_name} ---\n{doc_content}\n--- End of {attachment.file_name} ---\n"
                    )

            # Create MultiModalMessage
            multimodal_message = MultiModalMessage(content=content_parts, source=source)

            self.logger.info(
                f"Created MultiModalMessage with {len(content_parts)} content parts"
            )
            return multimodal_message

        except Exception as e:
            self.logger.error(f"Failed to create MultiModalMessage: {e}")
            raise ValueError(f"Failed to create multimodal message: {e}")

    def validate_attachments(self, attachments: List[MessageAttachment]) -> List[str]:
        """
        Validate attachments and return list of validation errors

        Args:
            attachments: List of attachments to validate

        Returns:
            List of validation error messages (empty if all valid)
        """
        errors = []

        for i, attachment in enumerate(attachments):
            # Check file type support
            if not self.is_supported_file_type(attachment.file_type):
                errors.append(
                    f"Attachment {i+1}: Unsupported file type '{attachment.file_type}'"
                )

            # Check file size (limit to 10MB for now)
            max_size = 10 * 1024 * 1024  # 10MB
            if attachment.file_size > max_size:
                errors.append(
                    f"Attachment {i+1}: File size {attachment.file_size} exceeds limit of {max_size} bytes"
                )

            # Check that either file_data or file_url is provided
            if not attachment.file_data and not attachment.file_url:
                errors.append(
                    f"Attachment {i+1}: Either file_data or file_url must be provided"
                )
            elif attachment.file_data and attachment.file_url:
                errors.append(
                    f"Attachment {i+1}: Provide either file_data or file_url, not both"
                )

            # Validate base64 data if provided
            if attachment.file_data:
                try:
                    self.decode_base64_file(attachment.file_data)
                except ValueError as e:
                    errors.append(f"Attachment {i+1}: Invalid base64 data - {e}")

            # Validate URL if provided
            if attachment.file_url:
                try:
                    parsed_url = urlparse(attachment.file_url)
                    if not parsed_url.scheme or not parsed_url.netloc:
                        errors.append(f"Attachment {i+1}: Invalid URL format")
                except Exception as e:
                    errors.append(f"Attachment {i+1}: URL validation error - {e}")

        return errors

    def get_attachment_summary(self, attachments: List[MessageAttachment]) -> str:
        """Generate a summary of attachments for logging/debugging"""
        if not attachments:
            return "No attachments"

        summary_parts = []
        for attachment in attachments:
            size_mb = attachment.file_size / (1024 * 1024)
            summary_parts.append(
                f"{attachment.file_name} ({attachment.file_type}, {size_mb:.2f}MB)"
            )

        return f"{len(attachments)} attachment(s): {', '.join(summary_parts)}"
