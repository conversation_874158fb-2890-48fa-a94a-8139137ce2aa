import asyncio
import json
import os
import logging
from typing import Any, Optional, Dict, List, Tuple
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.messages import TextMessage
from autogen_core.model_context import BufferedChatCompletionContext
from ..model_factory import ModelFactory

# Import settings directly without relative imports
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))
from app.shared.config.base import get_settings

logger = logging.getLogger(__name__)

# Disable autogen logging for cleaner output
for logger_name in [
    "autogen_agentchat",
    "autogen_core",
    "_single_threaded_agent_runtime",
    "autogen_runtime_core",
    "autogen_agentchat.teams",
    "autogen_agentchat.agents",
]:
    logger_to_silence = logging.getLogger(logger_name)
    logger_to_silence.setLevel(logging.CRITICAL)
    logger_to_silence.propagate = False


class DiscoveryMasterAgent:
    """
    Enhanced Discovery Master Agent that can route queries to specialized agents.
    
    This agent analyzes incoming queries and determines the best specialized agent
    to handle the request based on the agents registry.
    """

    def __init__(self, model_name: Optional[str] = None, api_key: Optional[str] = None):
        """
        Initialize the Discovery Master Agent.

        Args:
            model_name: The LLM model to use (uses DISCOVERY_LLM_MODEL env var if not provided)
            api_key: Google API key (uses settings if not provided)
        """
        self.settings = get_settings()
        self.logger = logger
        self.model_name = model_name or os.getenv("DISCOVERY_LLM_MODEL", "gemini-2.5-pro-preview-06-05")
        self.api_key = api_key or self.settings.requesty.api_key
        self.llm_type = os.getenv("DISCOVERY_LLM_TYPE", "google")
        
        self.master_agent = None
        self.context_messages = []
        self.agents_registry = []
        self._specialized_agents_cache = {}

    async def initialize(self) -> None:
        """Initialize the master agent and load the agents registry."""
        try:
            self.logger.info("Initializing Discovery Master Agent...")
            
            # Load agents registry
            await self._load_agents_registry()
            
            # Create model client using ModelFactory
            model_config = {
                "llm_type": self.llm_type,
                "provider": "GoogleChatCompletionClient" if self.llm_type == "google" else "OpenAIChatCompletionClient",
                "model": self.model_name,
                "api_key": self.api_key,
                "base_url": self.settings.requesty.base_url if self.llm_type == "google" else None,
            }

            chat_completion_client = ModelFactory.create_model_client(model_config)
            if not chat_completion_client:
                raise ValueError(f"Failed to create {self.llm_type} model client")

            # Create model context
            model_context = BufferedChatCompletionContext(buffer_size=32)

            # Define system message for discovery agent
            system_message = """You are a Discovery Master Agent responsible for routing user queries to the most appropriate specialized agent.

Your task is to:
1. Analyze the user's query to understand their intent and requirements
2. Determine which specialized agent from the registry would be best suited to handle the query
3. Route the query to that agent and return their response

You have access to a registry of specialized agents with different expertise areas. Always choose the agent that best matches the user's needs based on their specialization, expertise, and keywords.

If no suitable agent is found, politely inform the user about the available specializations and suggest how they might rephrase their query."""

            # Create the master agent
            self.master_agent = AssistantAgent(
                name="discovery_master_agent",
                description="Master agent for routing queries to specialized agents",
                model_client=chat_completion_client,
                system_message=system_message,
                model_context=model_context,
                tools=[],
                reflect_on_tool_use=False,
            )

            self.logger.info("Discovery Master Agent initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize Discovery Master Agent: {e}")
            raise e

    async def _load_agents_registry(self) -> None:
        """Load the specialized agents registry from JSON file."""
        try:
            registry_path = os.path.join(
                os.path.dirname(__file__), 
                "agents_registry.json"
            )
            
            with open(registry_path, 'r') as file:
                registry_data = json.load(file)
                self.agents_registry = registry_data.get("agents", [])
            
            self.logger.info(f"Loaded {len(self.agents_registry)} agents from registry")
            
        except Exception as e:
            self.logger.error(f"Failed to load agents registry: {e}")
            self.agents_registry = []

    async def _analyze_query_for_agent_selection(self, query: str) -> Tuple[Optional[Dict], str, float]:
        """
        Analyze the query to determine the best agent match.
        
        Args:
            query: User query to analyze
            
        Returns:
            Tuple of (selected_agent, reasoning, confidence_score)
        """
        try:
            # Create analysis prompt
            agents_summary = "\n".join([
                f"- {agent['name']} (ID: {agent['id']}): {agent['description']}\n"
                f"  Keywords: {', '.join(agent['keywords'])}\n"
                f"  Specialization: {agent['specialization']}"
                for agent in self.agents_registry
            ])

            analysis_prompt = f"""
            Analyze this user query and select the most suitable agent:
            
            Query: "{query}"
            
            Available Agents:
            {agents_summary}
            
            Please respond in this exact format:
            SELECTED_AGENT_ID: [agent_id or "none"]
            CONFIDENCE: [0.0-1.0]
            REASONING: [detailed explanation]
            
            If no agent is suitable (confidence < 0.3), select "none".
            """

            # Get analysis from the master agent
            text_message = TextMessage(content=analysis_prompt, source="user")
            response = await self.master_agent.on_messages([text_message], cancellation_token=None)
            
            analysis_result = ""
            if hasattr(response, 'chat_message') and hasattr(response.chat_message, 'content'):
                analysis_result = response.chat_message.content
            elif hasattr(response, 'content'):
                analysis_result = response.content
            else:
                analysis_result = str(response)

            # Parse the analysis result
            selected_agent_id = None
            confidence = 0.0
            reasoning = "No analysis provided"

            lines = analysis_result.split('\n')
            for line in lines:
                if line.startswith('SELECTED_AGENT_ID:'):
                    agent_id = line.split(':', 1)[1].strip()
                    if agent_id != "none":
                        selected_agent_id = agent_id
                elif line.startswith('CONFIDENCE:'):
                    try:
                        confidence = float(line.split(':', 1)[1].strip())
                    except ValueError:
                        confidence = 0.0
                elif line.startswith('REASONING:'):
                    reasoning = line.split(':', 1)[1].strip()

            # Find the selected agent
            selected_agent = None
            if selected_agent_id:
                for agent in self.agents_registry:
                    if agent['id'] == selected_agent_id:
                        selected_agent = agent
                        break

            self.logger.info(f"Agent selection - ID: {selected_agent_id}, Confidence: {confidence}")
            return selected_agent, reasoning, confidence

        except Exception as e:
            self.logger.error(f"Error in agent selection analysis: {e}")
            return None, f"Error in agent selection: {str(e)}", 0.0

    async def _create_specialized_agent(self, agent_config: Dict) -> AssistantAgent:
        """Create a specialized agent instance from configuration."""
        try:
            # Create model client for the specialized agent
            model_config = {
                "llm_type": self.llm_type,
                "provider": "GoogleChatCompletionClient" if self.llm_type == "google" else "OpenAIChatCompletionClient",
                "model": agent_config.get("model_name", self.model_name),
                "api_key": self.api_key,
                "base_url": self.settings.requesty.base_url if self.llm_type == "google" else None,
            }

            chat_completion_client = ModelFactory.create_model_client(model_config)
            if not chat_completion_client:
                raise ValueError("Failed to create model client for specialized agent")

            # Create fresh model context for the specialized agent
            model_context = BufferedChatCompletionContext(buffer_size=32)

            # Create the specialized agent using agent ID as name (valid Python identifier)
            specialized_agent = AssistantAgent(
                name=agent_config["id"],  # Use ID instead of name for valid Python identifier
                description=agent_config["description"],
                model_client=chat_completion_client,
                system_message=agent_config["system_message"],
                model_context=model_context,
                tools=[],  # No actual tools for now (dummy implementation)
                reflect_on_tool_use=False,
            )

            self.logger.info(f"Created specialized agent: {agent_config['name']} (ID: {agent_config['id']})")
            return specialized_agent

        except Exception as e:
            self.logger.error(f"Failed to create specialized agent {agent_config['name']}: {e}")
            raise e

    async def route_query(self, query: str) -> Dict[str, Any]:
        """
        Route a query to the most suitable specialized agent.
        
        Args:
            query: User query to route
            
        Returns:
            Dict containing routing results and response
        """
        if not self.master_agent:
            raise ValueError("Discovery Master Agent not initialized. Call initialize() first.")

        try:
            self.logger.info(f"Routing query: {query[:100]}...")

            # Analyze query to select best agent
            selected_agent, reasoning, confidence = await self._analyze_query_for_agent_selection(query)

            result = {
                "query": query,
                "selected_agent": None,
                "confidence": confidence,
                "reasoning": reasoning,
                "response": "",
                "success": False,
                "error": None
            }

            # If no suitable agent found
            if not selected_agent or confidence < 0.3:
                result["response"] = (
                    "I couldn't find a suitable specialized agent for your query. "
                    "This might be because your query doesn't match any of our available "
                    "specialized areas, or the confidence level was too low. "
                    f"Available specializations: {', '.join([agent['specialization'] for agent in self.agents_registry])}"
                )
                result["success"] = True
                self.logger.info(f"No suitable agent found. Confidence: {confidence}")
                return result

            # Create specialized agent if not cached
            agent_id = selected_agent["id"]
            if agent_id not in self._specialized_agents_cache:
                self._specialized_agents_cache[agent_id] = await self._create_specialized_agent(selected_agent)

            # Route query to selected agent
            specialized_agent = self._specialized_agents_cache[agent_id]
            
            # Create enhanced query with context
            enhanced_query = f"""
            As a {selected_agent['name']}, please help with this query:
            
            Query: {query}
            
            Use your expertise in {', '.join(selected_agent['expertise'])} to provide a comprehensive response.
            Available tools: {', '.join(selected_agent['tools'])}
            Available workflows: {', '.join(selected_agent['workflows'])}
            """

            text_message = TextMessage(content=enhanced_query, source="user")
            response = await specialized_agent.on_messages([text_message], cancellation_token=None)

            # Extract response content
            if hasattr(response, 'chat_message') and hasattr(response.chat_message, 'content'):
                agent_response = response.chat_message.content
            elif hasattr(response, 'content'):
                agent_response = response.content
            else:
                agent_response = str(response)

            result.update({
                "selected_agent": {
                    "id": selected_agent["id"],
                    "name": selected_agent["name"],
                    "specialization": selected_agent["specialization"],
                    "tools": selected_agent["tools"],
                    "workflows": selected_agent["workflows"]
                },
                "response": agent_response,
                "success": True
            })

            self.logger.info(f"Successfully routed query to {selected_agent['name']}")
            return result

        except Exception as e:
            self.logger.error(f"Error routing query: {e}")
            return {
                "query": query,
                "selected_agent": None,
                "confidence": 0.0,
                "reasoning": f"Error during routing: {str(e)}",
                "response": f"I encountered an error while processing your query: {str(e)}",
                "success": False,
                "error": str(e)
            }

    async def query(self, message: str) -> str:
        """
        Send a query and get a routed response (backward compatibility).
        
        Args:
            message: The query message
            
        Returns:
            str: The agent's response
        """
        result = await self.route_query(message)
        return result["response"]

    async def query_with_context(self, message: str, context: Optional[str] = None) -> str:
        """
        Send a query with additional context to the routing system.
        
        Args:
            message: The query message
            context: Additional context to include in the query
            
        Returns:
            str: The agent's response
        """
        if context:
            enhanced_message = f"Context: {context}\n\nQuery: {message}"
        else:
            enhanced_message = message

        return await self.query(enhanced_message)

    async def get_available_agents(self) -> List[Dict[str, Any]]:
        """Get list of available specialized agents."""
        return [
            {
                "id": agent["id"],
                "name": agent["name"],
                "description": agent["description"],
                "specialization": agent["specialization"],
                "expertise": agent["expertise"],
                "tools": agent["tools"],
                "workflows": agent["workflows"]
            }
            for agent in self.agents_registry
        ]

    async def reset_context(self) -> None:
        """Reset the agent's context by clearing the model context."""
        if self.master_agent:
            # Create a new model context to reset state
            self.master_agent.model_context = BufferedChatCompletionContext(buffer_size=32)
            
            # Also reset specialized agents
            self._specialized_agents_cache.clear()
            
            self.logger.info("Discovery Master Agent context reset successfully")

    def is_initialized(self) -> bool:
        """Check if the agent is initialized."""
        return self.master_agent is not None

    @classmethod
    async def create_and_initialize(
        cls,
        model_name: Optional[str] = None,
        api_key: Optional[str] = None,
    ) -> "DiscoveryMasterAgent":
        """
        Convenience method to create and initialize an agent in one call.

        Args:
            model_name: The LLM model to use (uses DISCOVERY_LLM_MODEL env var if not provided)
            api_key: API key

        Returns:
            DiscoveryMasterAgent: Initialized agent instance
        """
        agent = cls(model_name=model_name, api_key=api_key)
        await agent.initialize()
        return agent 