import asyncio
import json
import os
import logging
from typing import Any, Optional, Dict, List, Tuple
from autogen_agentchat.agents import Assistant<PERSON>gent, UserProxyAgent
from autogen_agentchat.messages import TextMessage
from autogen_agentchat.conditions import MaxMessageTermination, TextMentionTermination
from autogen_agentchat.teams import RoundRobinGroupChat, SelectorGroupChat
from autogen_core.model_context import BufferedChatCompletionContext
from ..model_factory import ModelFactory

# Import settings directly without relative imports
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))
from app.shared.config.base import get_settings

# Import the 4 specialized agents
from .query_analysis_agent import QueryAnalysisAgent
from .discovery_agent import DiscoveryAgent
from .selection_agent import SelectionAgent
from .assignment_agent import AssignmentAgent

logger = logging.getLogger(__name__)

# Disable autogen logging for cleaner output
for logger_name in [
    "autogen_agentchat",
    "autogen_core",
    "_single_threaded_agent_runtime",
    "autogen_runtime_core",
    "autogen_agentchat.teams",
    "autogen_agentchat.agents",
]:
    logger_to_silence = logging.getLogger(logger_name)
    logger_to_silence.setLevel(logging.CRITICAL)
    logger_to_silence.propagate = False


class DiscoveryMasterAgentGroupChat:
    """
    Enhanced Discovery Master Agent that orchestrates 4 specialized agents in a group chat:
    1. Query Analysis Agent - analyzes and extracts task requirements
    2. Discovery Agent - finds suitable agents from registry
    3. Selection Agent - chooses the best agent from discovered options
    4. Assignment Agent - creates detailed task assignment
    """

    def __init__(self, model_name: Optional[str] = None, api_key: Optional[str] = None):
        """
        Initialize the Discovery Master Agent with Group Chat.

        Args:
            model_name: The LLM model to use (uses DISCOVERY_LLM_MODEL env var if not provided)
            api_key: API key (uses settings if not provided)
        """
        self.settings = get_settings()
        self.logger = logger
        self.model_name = model_name or os.getenv("DISCOVERY_LLM_MODEL", "gemini-2.5-pro-preview-06-05")
        self.api_key = api_key or self.settings.requesty.api_key
        self.llm_type = os.getenv("DISCOVERY_LLM_TYPE", "google")
        
        # Individual agent instances
        self.query_agent = None
        self.discovery_agent = None
        self.selection_agent = None
        self.assignment_agent = None
        
        # Group chat components
        self.group_chat_team = None
        self.agents_registry = []
        self._specialized_agents_cache = {}

    async def initialize(self, chat_type: str = "round_robin") -> None:
        """
        Initialize all 4 agents and create group chat team.

        Args:
            chat_type: Type of group chat ("round_robin" or "selector")
        """
        try:
            self.logger.info("Initializing Discovery Master Agent with Group Chat...")

            # Initialize all 4 specialized agents
            await self._initialize_agents()

            # Create group chat team
            await self._create_group_chat_team(chat_type)

            self.logger.info("Discovery Master Agent Group Chat initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize Discovery Master Agent Group Chat: {e}")
            raise e

    async def _initialize_agents(self) -> None:
        """Initialize all 4 specialized agents concurrently."""
        try:
            # Initialize all agents concurrently for better performance
            self.query_agent, self.discovery_agent, self.selection_agent, self.assignment_agent = await asyncio.gather(
                QueryAnalysisAgent.create_and_initialize(
                    model_name=self.model_name,
                    api_key=self.api_key
                ),
                DiscoveryAgent.create_and_initialize(
                    model_name=self.model_name,
                    api_key=self.api_key
                ),
                SelectionAgent.create_and_initialize(
                    model_name=self.model_name,
                    api_key=self.api_key
                ),
                AssignmentAgent.create_and_initialize(
                    model_name=self.model_name,
                    api_key=self.api_key
                )
            )

            self.logger.info("All 4 specialized agents initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize specialized agents: {e}")
            raise e

    async def _create_group_chat_team(self, chat_type: str) -> None:
        """Create the group chat team with all agents."""
        try:
            # Get the underlying AssistantAgent instances
            autogen_agents = [
                self.query_agent.get_agent(),
                self.discovery_agent.get_agent(),
                self.selection_agent.get_agent(),
                self.assignment_agent.get_agent()
            ]

            # Add user proxy agent
            user_proxy = UserProxyAgent(name="user")
            autogen_agents.append(user_proxy)

            # Set more lenient termination conditions - allow more rounds for 4 agents to complete work
            text_mention_termination = TextMentionTermination("WORKFLOW_COMPLETE")  # Changed from "TERMINATE"
            max_messages_termination = MaxMessageTermination(max_messages=20)  # Increased from 12 to allow all agents to participate
            termination = text_mention_termination | max_messages_termination

            if chat_type == "round_robin":
                # Create round robin group chat - agents speak in order
                self.group_chat_team = RoundRobinGroupChat(
                    participants=autogen_agents,
                    termination_condition=termination
                )
            elif chat_type == "selector":
                # Create selector group chat - selector chooses who speaks next
                model_config = {
                    "llm_type": self.llm_type,
                    "provider": "GoogleChatCompletionClient" if self.llm_type == "google" else "OpenAIChatCompletionClient",
                    "model": self.model_name,
                    "api_key": self.api_key,
                    "base_url": self.settings.requesty.base_url if self.llm_type == "google" else None,
                }

                model_client = ModelFactory.create_model_client(model_config)

                self.group_chat_team = SelectorGroupChat(
                    participants=autogen_agents,
                    model_client=model_client,
                    termination_condition=termination
                )
            else:
                raise ValueError(f"Unsupported chat type: {chat_type}")

            self.logger.info(f"Group chat team created with {chat_type} strategy")

        except Exception as e:
            self.logger.error(f"Failed to create group chat team: {e}")
            raise e

    async def process_query_with_group_chat(self, query: str) -> Dict[str, Any]:
        """
        Process a query using the 4-agent group chat workflow.
        
        Args:
            query: User query to process
            
        Returns:
            Dict containing complete workflow results
        """
        if not self.group_chat_team:
            raise ValueError("Group chat not initialized. Call initialize() first.")

        try:
            self.logger.info(f"Processing query with group chat: {query[:100]}...")

            # Instead of relying on AutoGen's round-robin which seems to be failing,
            # let's manually orchestrate the agents in sequence to ensure they all participate
            workflow_results = []
            
            # Step 1: Query Analysis Agent
            self.logger.info("Step 1: Running Query Analysis Agent...")
            analysis_results = await self.query_agent.analyze_query(query)
            workflow_results.append({
                "agent": "query_analysis_agent",
                "content": f"TASK_SUMMARY: {analysis_results.get('task_summary', 'N/A')}\n"
                          f"DOMAIN: {analysis_results.get('domain', 'N/A')}\n"
                          f"REQUIREMENTS: {', '.join(analysis_results.get('requirements', []))}\n"
                          f"KEYWORDS: {', '.join(analysis_results.get('keywords', []))}\n"
                          f"URGENCY: {analysis_results.get('urgency', 'medium')}\n"
                          f"COMPLEXITY: {analysis_results.get('complexity', 'moderate')}",
                "timestamp": asyncio.get_event_loop().time()
            })
            
            # Step 2: Discovery Agent
            self.logger.info("Step 2: Running Discovery Agent...")
            discovery_results = await self.discovery_agent.discover_agents(analysis_results)
            discovered_agents = discovery_results.get('discovered_agents', [])
            discovery_content = "DISCOVERY_RESULTS:\n"
            for i, agent in enumerate(discovered_agents, 1):
                discovery_content += f"AGENT_{i}: {agent.get('agent_id', 'unknown')} | SCORE: {agent.get('score', 0.0)} | REASON: {agent.get('reason', 'N/A')}\n"
            discovery_content += f"SUMMARY: Found {len(discovered_agents)} suitable agents"
            
            workflow_results.append({
                "agent": "discovery_agent", 
                "content": discovery_content,
                "timestamp": asyncio.get_event_loop().time()
            })
            
            # Step 3: Selection Agent
            self.logger.info("Step 3: Running Selection Agent...")
            selection_results = await self.selection_agent.select_agent(discovery_results)
            selected_agent = selection_results.get('selected_agent')
            selection_content = f"SELECTED_AGENT: {selected_agent.get('agent_id', 'None') if selected_agent else 'None'}\n"
            selection_content += f"CONFIDENCE: {selection_results.get('confidence', 0.0)}\n"
            selection_content += f"PRIMARY_REASON: {selection_results.get('reason', 'N/A')}"
            
            workflow_results.append({
                "agent": "selection_agent",
                "content": selection_content,
                "timestamp": asyncio.get_event_loop().time()
            })
            
            # Step 4: Assignment Agent  
            self.logger.info("Step 4: Running Assignment Agent...")
            assignment_results = await self.assignment_agent.create_assignment(selection_results)
            assignment_details = assignment_results.get('assignment_details', {})
            assignment_content = f"TASK_ASSIGNMENT:\n"
            assignment_content += f"ASSIGNED_TO: {assignment_details.get('assigned_to', 'N/A')}\n"
            assignment_content += f"PRIORITY: {assignment_details.get('priority', 'medium')}\n"
            assignment_content += f"OBJECTIVE: {assignment_details.get('objective', 'N/A')}\n"
            assignment_content += f"DELIVERABLES: {', '.join(assignment_details.get('deliverables', []))}\n"
            assignment_content += "WORKFLOW_COMPLETE"
            
            workflow_results.append({
                "agent": "assignment_agent",
                "content": assignment_content,
                "timestamp": asyncio.get_event_loop().time()
            })

            # Parse and structure the workflow results
            parsed_results = self._parse_group_chat_results(workflow_results, query)
            
            # Add the actual data we computed
            parsed_results.update({
                "analysis": analysis_results,
                "discovery": discovery_results,
                "selection": selection_results,
                "assignment": assignment_results
            })
            
            self.logger.info(f"Group chat workflow completed successfully - found {len(discovered_agents)} agents")
            return parsed_results

        except Exception as e:
            self.logger.error(f"Error in group chat workflow: {e}")
            return {
                "query": query,
                "success": False,
                "error": str(e),
                "workflow_results": [],
                "analysis": {},
                "discovery": {},
                "selection": {},
                "assignment": {},
                "final_assignment": "Error occurred during group chat workflow"
            }

    def _parse_group_chat_results(self, workflow_results: List[Dict], query: str) -> Dict[str, Any]:
        """Parse the group chat results into structured workflow output."""
        result = {
            "query": query,
            "success": True,
            "workflow_results": workflow_results,
            "analysis": {},
            "discovery": {},
            "selection": {},
            "assignment": {},
            "final_assignment": ""
        }

        try:
            # Extract results from each agent
            for msg in workflow_results:
                agent_name = msg.get("agent", "").lower()
                content = msg.get("content", "")

                if "query_analysis_agent" in agent_name:
                    result["analysis"] = self._extract_analysis_from_content(content)
                elif "discovery_agent" in agent_name:
                    result["discovery"] = self._extract_discovery_from_content(content)
                elif "selection_agent" in agent_name:
                    result["selection"] = self._extract_selection_from_content(content)
                elif "assignment_agent" in agent_name:
                    result["assignment"] = self._extract_assignment_from_content(content)
                    result["final_assignment"] = content  # Use the full assignment content

            # If no final assignment was captured, create a summary
            if not result["final_assignment"]:
                result["final_assignment"] = self._create_workflow_summary(result)

        except Exception as e:
            self.logger.error(f"Error parsing group chat results: {e}")
            result["success"] = False
            result["error"] = str(e)

        return result

    def _extract_analysis_from_content(self, content: str) -> Dict[str, Any]:
        """Extract analysis results from query analysis agent content."""
        analysis = {"task_summary": "", "domain": "", "requirements": [], "keywords": []}
        
        try:
            lines = content.split('\n')
            for line in lines:
                line = line.strip()
                if ':' in line:
                    key, value = line.split(':', 1)
                    key = key.strip().lower()
                    value = value.strip()

                    if key == "task_summary":
                        analysis["task_summary"] = value
                    elif key == "domain":
                        analysis["domain"] = value
                    elif key == "requirements":
                        analysis["requirements"] = [req.strip() for req in value.split(',') if req.strip()]
                    elif key == "keywords":
                        analysis["keywords"] = [kw.strip() for kw in value.split(',') if kw.strip()]
        except Exception as e:
            self.logger.warning(f"Failed to parse analysis content: {e}")

        return analysis

    def _extract_discovery_from_content(self, content: str) -> Dict[str, Any]:
        """Extract discovery results from discovery agent content."""
        discovery = {"discovered_agents": [], "summary": ""}
        
        try:
            # Add debugging to see what content we're parsing
            self.logger.info(f"Extracting discovery from content: {content[:200]}...")
            
            lines = content.split('\n')
            current_section = None
            
            for line in lines:
                line = line.strip()
                if not line:  # Skip empty lines
                    continue
                
                # Look for section headers
                if line.startswith("DISCOVERY_RESULTS:"):
                    current_section = "agents"
                    continue
                elif line.startswith("SUMMARY:"):
                    current_section = "summary"
                    # Extract summary from the same line
                    summary_parts = line.split(":", 1)
                    if len(summary_parts) > 1:
                        discovery["summary"] = summary_parts[1].strip()
                    continue
                elif current_section == "summary" and line:
                    # Continue building summary if it spans multiple lines
                    if discovery["summary"]:
                        discovery["summary"] += " " + line
                    else:
                        discovery["summary"] = line
                    continue
                
                # Parse agent lines - be more flexible with the format
                if current_section == "agents" and ("AGENT" in line.upper() or "agent" in line.lower()):
                    agent_info = self._extract_agent_info_from_line(line)
                    if agent_info:
                        discovery["discovered_agents"].append(agent_info)
                        self.logger.info(f"Group chat extracted agent: {agent_info['agent_id']} with score {agent_info['score']}")
                
                # Also try to parse lines that contain scores or agent IDs even without strict format
                elif "score" in line.lower() and ("|" in line or ":" in line):
                    agent_info = self._extract_agent_info_from_line(line)
                    if agent_info:
                        discovery["discovered_agents"].append(agent_info)
            
            # If we still found no agents, try a more aggressive parsing approach
            if not discovery["discovered_agents"]:
                self.logger.warning("Group chat: No agents found with strict parsing, trying flexible approach...")
                discovery["discovered_agents"] = self._parse_discovery_content_flexible(content)
            
            self.logger.info(f"Group chat discovery extraction: found {len(discovery['discovered_agents'])} agents")
            
        except Exception as e:
            self.logger.warning(f"Failed to parse discovery content: {e}")

        return discovery

    def _extract_agent_info_from_line(self, line: str) -> Optional[Dict[str, Any]]:
        """Extract agent information from a line for group chat parsing."""
        try:
            # Initialize result
            agent_info = {
                "agent_id": "",
                "score": 0.0
            }
            
            # Split by | first, then try other delimiters
            if "|" in line:
                parts = line.split("|")
            elif ";" in line:
                parts = line.split(";")
            else:
                parts = [line]
            
            for part in parts:
                part = part.strip()
                
                # Look for agent ID
                if ":" in part and ("agent" in part.lower()):
                    potential_id = part.split(":")[-1].strip()
                    # Validate that this could be an agent ID (contains underscores and numbers)
                    if "_" in potential_id and any(char.isdigit() for char in potential_id):
                        agent_info["agent_id"] = potential_id
                
                # Look for score
                if "score" in part.lower() and ":" in part:
                    try:
                        score_str = part.split(":")[-1].strip()
                        # Extract numeric value
                        import re
                        score_match = re.search(r'(\d+\.?\d*)', score_str)
                        if score_match:
                            agent_info["score"] = float(score_match.group(1))
                    except:
                        pass
            
            # Also try to find agent IDs directly in the line (fallback)
            if not agent_info["agent_id"]:
                # Look for patterns like "word_word_digits"
                import re
                agent_id_pattern = r'\b([a-z_]+_\d{3})\b'
                matches = re.findall(agent_id_pattern, line)
                if matches:
                    agent_info["agent_id"] = matches[0]
            
            # Return only if we found an agent ID
            return agent_info if agent_info["agent_id"] else None
            
        except Exception as e:
            self.logger.debug(f"Error in group chat flexible parsing: {e}")
            return None

    def _parse_discovery_content_flexible(self, content: str) -> List[Dict[str, Any]]:
        """Last resort flexible parsing for group chat to find any agent mentions."""
        found_agents = []
        
        try:
            # Look for common agent ID patterns in the content
            import re
            
            # Pattern for agent IDs like "code_analyzer_001", "data_scientist_002", etc.
            agent_id_pattern = r'\b([a-z_]+_\d{3})\b'
            agent_matches = re.findall(agent_id_pattern, content)
            
            for agent_id in agent_matches:
                # Try to find a score near this agent ID
                score = 0.5  # Default score
                
                # Look for numbers that might be scores near the agent ID
                agent_context = content.replace(agent_id, f"<AGENT>{agent_id}</AGENT>")
                parts = agent_context.split(f"<AGENT>{agent_id}</AGENT>")
                
                if len(parts) > 1:
                    # Look in the text around the agent ID
                    context = (parts[0][-100:] + parts[1][:100]).lower()
                    score_matches = re.findall(r'(\d+\.?\d*)', context)
                    for match in score_matches:
                        try:
                            potential_score = float(match)
                            if 0.0 <= potential_score <= 1.0:
                                score = potential_score
                                break
                            elif 0.0 <= potential_score <= 10.0:  # Could be out of 10
                                score = potential_score / 10.0
                                break
                        except:
                            continue
                
                found_agents.append({
                    "agent_id": agent_id,
                    "score": score
                })
                
                self.logger.info(f"Group chat flexibly found agent: {agent_id} with score {score}")
        
        except Exception as e:
            self.logger.error(f"Error in group chat flexible discovery parsing: {e}")
        
        return found_agents

    def _extract_selection_from_content(self, content: str) -> Dict[str, Any]:
        """Extract selection results from selection agent content."""
        selection = {"selected_agent_id": "", "confidence": 0.0, "reason": ""}
        
        try:
            lines = content.split('\n')
            for line in lines:
                line = line.strip()
                if ':' in line:
                    key, value = line.split(':', 1)
                    key = key.strip().lower()
                    value = value.strip()

                    if key == "selected_agent":
                        selection["selected_agent_id"] = value
                    elif key == "confidence":
                        try:
                            selection["confidence"] = float(value)
                        except ValueError:
                            pass
                    elif key == "primary_reason":
                        selection["reason"] = value
        except Exception as e:
            self.logger.warning(f"Failed to parse selection content: {e}")

        return selection

    def _extract_assignment_from_content(self, content: str) -> Dict[str, Any]:
        """Extract assignment details from assignment agent content."""
        assignment = {"assigned_to": "", "objective": "", "deliverables": []}
        
        try:
            lines = content.split('\n')
            for line in lines:
                line = line.strip()
                if ':' in line:
                    key, value = line.split(':', 1)
                    key = key.strip().lower()
                    value = value.strip()

                    if key == "assigned_to":
                        assignment["assigned_to"] = value
                    elif key == "objective":
                        assignment["objective"] = value
                    elif key == "deliverables":
                        assignment["deliverables"] = [deliv.strip() for deliv in value.split(',') if deliv.strip()]
        except Exception as e:
            self.logger.warning(f"Failed to parse assignment content: {e}")

        return assignment

    def _create_workflow_summary(self, result: Dict[str, Any]) -> str:
        """Create a summary of the workflow if no final assignment was captured."""
        analysis = result.get("analysis", {})
        discovery = result.get("discovery", {})
        selection = result.get("selection", {})
        
        summary = f"""
Workflow Summary for: {result.get('query', 'Unknown query')}

Analysis: {analysis.get('task_summary', 'No analysis available')}
Domain: {analysis.get('domain', 'Unknown')}

Discovery: Found {len(discovery.get('discovered_agents', []))} suitable agents
Selection: Selected agent {selection.get('selected_agent_id', 'None')} with confidence {selection.get('confidence', 0.0)}

The 4-agent discovery workflow has been completed.
        """.strip()
        
        return summary

    async def route_to_specialized_agent(self, assignment_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the assignment by routing to the actual specialized agent.
        
        Args:
            assignment_results: Results from the group chat workflow
            
        Returns:
            Final response from the specialized agent
        """
        try:
            selection = assignment_results.get("selection", {})
            selected_agent_id = selection.get("selected_agent_id")
            
            if not selected_agent_id:
                return {
                    "success": False,
                    "response": "No agent was selected for execution",
                    "assignment_results": assignment_results
                }

            # Load agents registry if not already loaded
            if not self.agents_registry:
                await self._load_agents_registry()

            # Find the selected agent configuration
            selected_agent_config = None
            for agent in self.agents_registry:
                if agent["id"] == selected_agent_id:
                    selected_agent_config = agent
                    break

            if not selected_agent_config:
                return {
                    "success": False,
                    "response": f"Agent configuration not found for ID: {selected_agent_id}",
                    "assignment_results": assignment_results
                }

            # Create or get cached specialized agent
            if selected_agent_id not in self._specialized_agents_cache:
                specialized_agent = await self._create_specialized_agent(selected_agent_config)
                self._specialized_agents_cache[selected_agent_id] = specialized_agent
            else:
                specialized_agent = self._specialized_agents_cache[selected_agent_id]

            # Format the assignment for execution
            assignment_message = assignment_results.get("final_assignment", "")
            if not assignment_message:
                assignment_message = f"Please help with: {assignment_results.get('query', '')}"

            # Execute the task with the specialized agent
            text_message = TextMessage(content=assignment_message, source="user")
            response = await specialized_agent.on_messages([text_message], cancellation_token=None)

            # Extract response content
            if hasattr(response, 'chat_message') and hasattr(response.chat_message, 'content'):
                agent_response = response.chat_message.content
            elif hasattr(response, 'content'):
                agent_response = response.content
            else:
                agent_response = str(response)

            return {
                "success": True,
                "response": agent_response,
                "selected_agent": selected_agent_config,
                "assignment_results": assignment_results
            }

        except Exception as e:
            self.logger.error(f"Error routing to specialized agent: {e}")
            return {
                "success": False,
                "response": f"Error executing with specialized agent: {str(e)}",
                "assignment_results": assignment_results,
                "error": str(e)
            }

    async def _load_agents_registry(self) -> None:
        """Load the specialized agents registry from JSON file."""
        try:
            registry_path = os.path.join(
                os.path.dirname(__file__), 
                "agents_registry.json"
            )
            
            with open(registry_path, 'r') as file:
                registry_data = json.load(file)
                self.agents_registry = registry_data.get("agents", [])
            
            self.logger.info(f"Loaded {len(self.agents_registry)} agents from registry")
            
        except Exception as e:
            self.logger.error(f"Failed to load agents registry: {e}")
            self.agents_registry = []

    async def _create_specialized_agent(self, agent_config: Dict) -> AssistantAgent:
        """Create a specialized agent instance from configuration."""
        try:
            # Create model client for the specialized agent
            model_config = {
                "llm_type": self.llm_type,
                "provider": "GoogleChatCompletionClient" if self.llm_type == "google" else "OpenAIChatCompletionClient",
                "model": agent_config.get("model_name", self.model_name),
                "api_key": self.api_key,
                "base_url": self.settings.requesty.base_url if self.llm_type == "google" else None,
            }

            chat_completion_client = ModelFactory.create_model_client(model_config)
            if not chat_completion_client:
                raise ValueError("Failed to create model client for specialized agent")

            # Create fresh model context for the specialized agent
            model_context = BufferedChatCompletionContext(buffer_size=32)

            # Create the specialized agent
            specialized_agent = AssistantAgent(
                name=agent_config["id"],
                description=agent_config["description"],
                model_client=chat_completion_client,
                system_message=agent_config["system_message"],
                model_context=model_context,
                tools=[],
                reflect_on_tool_use=False,
            )

            self.logger.info(f"Created specialized agent: {agent_config['name']} (ID: {agent_config['id']})")
            return specialized_agent

        except Exception as e:
            self.logger.error(f"Failed to create specialized agent {agent_config['name']}: {e}")
            raise e

    async def process_query_end_to_end(self, query: str) -> Dict[str, Any]:
        """
        Complete end-to-end query processing: group chat workflow + specialized agent execution.
        
        Args:
            query: User query to process
            
        Returns:
            Complete results including workflow and final response
        """
        try:
            self.logger.info(f"Starting end-to-end processing: {query[:100]}...")

            # Step 1: Run the 4-agent group chat workflow
            workflow_results = await self.process_query_with_group_chat(query)
            
            if not workflow_results.get("success"):
                return workflow_results

            # Step 2: Execute with the selected specialized agent
            execution_results = await self.route_to_specialized_agent(workflow_results)

            # Combine results
            return {
                "query": query,
                "success": execution_results.get("success", False),
                "workflow": workflow_results,
                "execution": execution_results,
                "final_response": execution_results.get("response", "No response generated"),
                "selected_agent": execution_results.get("selected_agent"),
                "process_type": "end_to_end"
            }

        except Exception as e:
            self.logger.error(f"Error in end-to-end processing: {e}")
            return {
                "query": query,
                "success": False,
                "error": str(e),
                "final_response": f"Error in end-to-end processing: {str(e)}",
                "process_type": "end_to_end"
            }

    async def process_query_with_detailed_logging(self, query: str) -> Dict[str, Any]:
        """
        Complete end-to-end query processing with detailed step-by-step logging.
        Shows exactly what each agent is doing, their inputs, and outputs.
        
        Args:
            query: User query to process
            
        Returns:
            Complete results including workflow and final response
        """
        if not self.group_chat_team:
            raise ValueError("Group chat not initialized. Call initialize() first.")

        try:
            print(f"\n🚀 Starting 4-Agent Discovery Workflow")
            print(f"📝 User Query: {query}")
            print("-" * 60)

            workflow_results = []
            
            # Step 1: Query Analysis Agent
            print(f"\n🔍 STEP 1: Query Analysis Agent")
            print(f"📥 Input: User query to analyze")
            print(f"   '{query}'")
            print(f"🤖 Query Analysis Agent is processing...")
            
            analysis_results = await self.query_agent.analyze_query(query)
            
            analysis_content = f"TASK_SUMMARY: {analysis_results.get('task_summary', 'N/A')}\n"
            analysis_content += f"DOMAIN: {analysis_results.get('domain', 'N/A')}\n"
            analysis_content += f"REQUIREMENTS: {', '.join(analysis_results.get('requirements', []))}\n"
            analysis_content += f"KEYWORDS: {', '.join(analysis_results.get('keywords', []))}\n"
            analysis_content += f"URGENCY: {analysis_results.get('urgency', 'medium')}\n"
            analysis_content += f"COMPLEXITY: {analysis_results.get('complexity', 'moderate')}"
            
            print(f"📤 Query Analysis Agent Output:")
            print(f"   Domain: {analysis_results.get('domain', 'N/A')}")
            print(f"   Task: {analysis_results.get('task_summary', 'N/A')}")
            print(f"   Requirements: {', '.join(analysis_results.get('requirements', []))}")
            print(f"   Keywords: {', '.join(analysis_results.get('keywords', []))}")
            print(f"✅ Query Analysis Agent completed")
            
            workflow_results.append({
                "agent": "query_analysis_agent",
                "content": analysis_content,
                "timestamp": asyncio.get_event_loop().time()
            })
            
            # Step 2: Discovery Agent
            print(f"\n🔎 STEP 2: Discovery Agent")
            print(f"📥 Input: Analysis results from Query Analysis Agent")
            print(f"   Domain: {analysis_results.get('domain', 'N/A')}")
            print(f"   Keywords: {', '.join(analysis_results.get('keywords', []))}")
            print(f"🤖 Discovery Agent is searching agent registry...")
            
            discovery_results = await self.discovery_agent.discover_agents(analysis_results)
            discovered_agents = discovery_results.get('discovered_agents', [])
            
            print(f"📤 Discovery Agent Output:")
            print(f"   Found {len(discovered_agents)} suitable agents:")
            for i, agent in enumerate(discovered_agents, 1):
                print(f"   {i}. {agent.get('agent_name', 'Unknown')} (Score: {agent.get('score', 0.0)})")
                print(f"      ID: {agent.get('agent_id', 'unknown')}")
                print(f"      Reason: {agent.get('reason', 'N/A')}")
            print(f"✅ Discovery Agent completed")
            
            discovery_content = "DISCOVERY_RESULTS:\n"
            for i, agent in enumerate(discovered_agents, 1):
                discovery_content += f"AGENT_{i}: {agent.get('agent_id', 'unknown')} | SCORE: {agent.get('score', 0.0)} | REASON: {agent.get('reason', 'N/A')}\n"
            discovery_content += f"SUMMARY: Found {len(discovered_agents)} suitable agents"
            
            workflow_results.append({
                "agent": "discovery_agent", 
                "content": discovery_content,
                "timestamp": asyncio.get_event_loop().time()
            })
            
            # Step 3: Selection Agent
            print(f"\n🎯 STEP 3: Selection Agent")
            print(f"📥 Input: Discovery results with {len(discovered_agents)} candidate agents")
            for i, agent in enumerate(discovered_agents, 1):
                print(f"   Candidate {i}: {agent.get('agent_name', 'Unknown')} (Score: {agent.get('score', 0.0)})")
            print(f"🤖 Selection Agent is evaluating candidates...")
            
            selection_results = await self.selection_agent.select_agent(discovery_results)
            selected_agent = selection_results.get('selected_agent')
            
            print(f"📤 Selection Agent Output:")
            if selected_agent:
                print(f"   Selected: {selected_agent.get('agent_name', 'Unknown')}")
                print(f"   Agent ID: {selected_agent.get('agent_id', 'N/A')}")
                print(f"   Confidence: {selection_results.get('confidence', 0.0)}")
                print(f"   Reason: {selection_results.get('reason', 'N/A')}")
            else:
                print(f"   No agent selected")
                print(f"   Reason: {selection_results.get('reason', 'No suitable agent found')}")
            print(f"✅ Selection Agent completed")
            
            selection_content = f"SELECTED_AGENT: {selected_agent.get('agent_id', 'None') if selected_agent else 'None'}\n"
            selection_content += f"CONFIDENCE: {selection_results.get('confidence', 0.0)}\n"
            selection_content += f"PRIMARY_REASON: {selection_results.get('reason', 'N/A')}"
            
            workflow_results.append({
                "agent": "selection_agent",
                "content": selection_content,
                "timestamp": asyncio.get_event_loop().time()
            })
            
            # Step 4: Assignment Agent  
            print(f"\n📋 STEP 4: Assignment Agent")
            if selected_agent:
                print(f"📥 Input: Selected agent for task assignment")
                print(f"   Agent: {selected_agent.get('agent_name', 'Unknown')}")
                print(f"   Task: {analysis_results.get('task_summary', 'N/A')}")
            else:
                print(f"📥 Input: No agent selected - will create 'no assignment' result")
            print(f"🤖 Assignment Agent is creating task assignment...")
            
            assignment_results = await self.assignment_agent.create_assignment(selection_results)
            assignment_details = assignment_results.get('assignment_details', {})
            
            print(f"📤 Assignment Agent Output:")
            if assignment_results.get('assignment_created'):
                print(f"   Assigned To: {assignment_details.get('assigned_to', 'N/A')}")
                print(f"   Priority: {assignment_details.get('priority', 'medium')}")
                print(f"   Objective: {assignment_details.get('objective', 'N/A')}")
                print(f"   Deliverables: {', '.join(assignment_details.get('deliverables', []))}")
            else:
                print(f"   Assignment failed: {assignment_results.get('reason', 'Unknown error')}")
            print(f"✅ Assignment Agent completed")
            
            assignment_content = f"TASK_ASSIGNMENT:\n"
            assignment_content += f"ASSIGNED_TO: {assignment_details.get('assigned_to', 'N/A')}\n"
            assignment_content += f"PRIORITY: {assignment_details.get('priority', 'medium')}\n"
            assignment_content += f"OBJECTIVE: {assignment_details.get('objective', 'N/A')}\n"
            assignment_content += f"DELIVERABLES: {', '.join(assignment_details.get('deliverables', []))}\n"
            assignment_content += "WORKFLOW_COMPLETE"
            
            workflow_results.append({
                "agent": "assignment_agent",
                "content": assignment_content,
                "timestamp": asyncio.get_event_loop().time()
            })

            print(f"\n🎉 4-Agent Workflow Complete!")
            print("-" * 60)

            # Parse and structure the workflow results
            parsed_results = self._parse_group_chat_results(workflow_results, query)
            
            # Add the actual data we computed
            parsed_results.update({
                "analysis": analysis_results,
                "discovery": discovery_results,
                "selection": selection_results,
                "assignment": assignment_results
            })
            
            # Step 5: Execute with specialized agent if one was selected
            if selected_agent and assignment_results.get('assignment_created'):
                print(f"\n🚀 STEP 5: Specialized Agent Execution")
                print(f"📥 Input: Task assignment for {selected_agent.get('agent_name', 'Unknown Agent')}")
                print(f"   Objective: {assignment_details.get('objective', 'N/A')}")
                print(f"🤖 {selected_agent.get('agent_name', 'Unknown Agent')} is executing the task...")
                
                execution_results = await self.route_to_specialized_agent(parsed_results)
                
                print(f"📤 {selected_agent.get('agent_name', 'Unknown Agent')} Output:")
                if execution_results.get('success'):
                    response = execution_results.get('response', 'No response')
                    print(f"   Response: {response[:200]}{'...' if len(response) > 200 else ''}")
                    print(f"✅ Specialized Agent execution completed")
                else:
                    print(f"   Error: {execution_results.get('response', 'Unknown error')}")
                    print(f"❌ Specialized Agent execution failed")
                
                # Combine results
                return {
                    "query": query,
                    "success": execution_results.get("success", False),
                    "workflow": parsed_results,
                    "execution": execution_results,
                    "final_response": execution_results.get("response", "No response generated"),
                    "selected_agent": execution_results.get("selected_agent"),
                    "process_type": "detailed_logging"
                }
            else:
                print(f"\n❌ No agent was selected or assignment failed")
                print(f"   Cannot proceed to specialized agent execution")
                
                return {
                    "query": query,
                    "success": False,
                    "workflow": parsed_results,
                    "execution": {
                        "success": False,
                        "response": "Agent not found",
                        "selected_agent": None
                    },
                    "final_response": "Agent not found",
                    "selected_agent": None,
                    "process_type": "detailed_logging"
                }
            
        except Exception as e:
            self.logger.error(f"Error in detailed logging workflow: {e}")
            print(f"\n❌ Error in workflow: {e}")
            return {
                "query": query,
                "success": False,
                "error": str(e),
                "workflow": {},
                "execution": {
                    "success": False,
                    "response": "Agent not found",
                    "selected_agent": None
                },
                "final_response": "Agent not found",
                "process_type": "detailed_logging"
            }

    def is_initialized(self) -> bool:
        """Check if the group chat system is initialized."""
        return (self.query_agent is not None and 
                self.discovery_agent is not None and 
                self.selection_agent is not None and 
                self.assignment_agent is not None and 
                self.group_chat_team is not None)

    @classmethod
    async def create_and_initialize(
        cls,
        model_name: Optional[str] = None,
        api_key: Optional[str] = None,
        chat_type: str = "round_robin",
    ) -> "DiscoveryMasterAgentGroupChat":
        """
        Convenience method to create and initialize the group chat system in one call.

        Args:
            model_name: The LLM model to use (uses DISCOVERY_LLM_MODEL env var if not provided)
            api_key: API key
            chat_type: Type of group chat ("round_robin" or "selector")

        Returns:
            DiscoveryMasterAgentGroupChat: Initialized group chat system
        """
        agent = cls(model_name=model_name, api_key=api_key)
        await agent.initialize(chat_type=chat_type)
        return agent 