import asyncio
import json
import os
import logging
from typing import Any, Optional, Dict, List
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.messages import TextMessage
from autogen_core.model_context import BufferedChatCompletionContext
from ..model_factory import ModelFactory

# Import settings directly without relative imports
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))
from app.shared.config.base import get_settings

logger = logging.getLogger(__name__)


class DiscoveryAgent:
    """
    Agent responsible for discovering suitable agents from the registry 
    based on analyzed task requirements.
    """

    def __init__(self, model_name: Optional[str] = None, api_key: Optional[str] = None):
        """
        Initialize the Discovery Agent.

        Args:
            model_name: The LLM model to use (uses DISCOVERY_LLM_MODEL env var if not provided)
            api_key: API key (uses settings if not provided)
        """
        self.settings = get_settings()
        self.logger = logger
        self.model_name = model_name or os.getenv("DISCOVERY_LLM_MODEL", "gemini-2.5-pro-preview-06-05")
        self.api_key = api_key or self.settings.requesty.api_key
        self.llm_type = os.getenv("DISCOVERY_LLM_TYPE", "google")
        self.agent = None
        self.agents_registry = []

    async def initialize(self) -> None:
        """Initialize the agent with model client, context, and load agents registry."""
        try:
            self.logger.info("Initializing Discovery Agent...")

            # Load agents registry
            await self._load_agents_registry()

            # Create model client using ModelFactory
            model_config = {
                "llm_type": self.llm_type,
                "provider": "GoogleChatCompletionClient" if self.llm_type == "google" else "OpenAIChatCompletionClient",
                "model": self.model_name,
                "api_key": self.api_key,
                "base_url": self.settings.requesty.base_url if self.llm_type == "google" else None,
            }

            chat_completion_client = ModelFactory.create_model_client(model_config)
            if not chat_completion_client:
                raise ValueError(f"Failed to create {self.llm_type} model client")

            # Create model context
            model_context = BufferedChatCompletionContext(buffer_size=32)

            # Define system message for discovery
            system_message = """You are a Discovery Agent specialized in finding the most suitable specialized agents for user tasks.

Your task is to:
1. Receive query analysis from the Query Analysis Agent
2. Search through the agents registry to find agents that match the requirements
3. Score and rank agents based on relevance to the task
4. Return a list of discovered agents with their scores and reasoning

Focus on matching agent specializations, expertise areas, and keywords with the analyzed query requirements."""

            # Create the assistant agent
            self.agent = AssistantAgent(
                name="discovery_agent",
                description="Discovers and scores specialized agents based on query analysis",
                model_client=chat_completion_client,
                system_message=system_message,
                model_context=model_context,
                tools=[],
                reflect_on_tool_use=False,
            )

            self.logger.info(f"Discovery Agent initialized successfully with {len(self.agents_registry)} agents in registry")

        except Exception as e:
            self.logger.error(f"Failed to initialize Discovery Agent: {e}")
            raise e

    async def _load_agents_registry(self) -> None:
        """Load the specialized agents registry from JSON file."""
        try:
            registry_path = os.path.join(
                os.path.dirname(__file__), 
                "agents_registry.json"
            )
            
            with open(registry_path, 'r') as file:
                registry_data = json.load(file)
                self.agents_registry = registry_data.get("agents", [])
            
            self.logger.info(f"Loaded {len(self.agents_registry)} agents from registry")
            
        except Exception as e:
            self.logger.error(f"Failed to load agents registry: {e}")
            self.agents_registry = []

    async def discover_agents(self, task_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Discover suitable agents based on task analysis.
        
        Args:
            task_analysis: Analyzed task requirements from QueryAnalysisAgent
            
        Returns:
            Dict containing discovery results
        """
        if not self.agent:
            raise ValueError("Discovery Agent not initialized. Call initialize() first.")

        try:
            self.logger.info(f"Discovering agents for domain: {task_analysis.get('domain', 'unknown')}")

            # Create discovery prompt with task analysis
            discovery_prompt = f"""
            Please discover the most suitable agents for this analyzed task:
            
            TASK ANALYSIS:
            - Task Summary: {task_analysis.get('task_summary', 'No summary')}
            - Domain: {task_analysis.get('domain', 'unknown')}
            - Requirements: {', '.join(task_analysis.get('requirements', []))}
            - Constraints: {', '.join(task_analysis.get('constraints', []))}
            - Technologies: {', '.join(task_analysis.get('technologies', []))}
            - Output Type: {task_analysis.get('output_type', 'unknown')}
            - Urgency: {task_analysis.get('urgency', 'medium')}
            - Complexity: {task_analysis.get('complexity', 'moderate')}
            - Keywords: {', '.join(task_analysis.get('keywords', []))}
            
            AVAILABLE AGENTS IN REGISTRY:
            {self._format_agents_for_discovery()}
            
            Please respond in exactly this format:
            
            DISCOVERED_AGENTS:
            1. AGENT_ID: [agent_id] | SCORE: [0.0-1.0] | REASON: [detailed reasoning]
            2. AGENT_ID: [agent_id] | SCORE: [0.0-1.0] | REASON: [detailed reasoning]
            3. AGENT_ID: [agent_id] | SCORE: [0.0-1.0] | REASON: [detailed reasoning]
            
            SUMMARY: [brief summary of discovery results]
            
            Only suggest agents that exist in the registry above. Score based on relevance to the task.
            """

            text_message = TextMessage(content=discovery_prompt, source="user")
            response = await self.agent.on_messages([text_message], cancellation_token=None)

            # Extract response content
            if hasattr(response, 'chat_message') and hasattr(response.chat_message, 'content'):
                discovery_result = response.chat_message.content
            elif hasattr(response, 'content'):
                discovery_result = response.content
            else:
                discovery_result = str(response)

            # Parse the discovery results
            parsed_results = self._parse_discovery_result(discovery_result)
            parsed_results["task_analysis"] = task_analysis
            parsed_results["raw_discovery"] = discovery_result

            discovered_count = len(parsed_results.get("discovered_agents", []))
            self.logger.info(f"Discovery completed: found {discovered_count} suitable agents")
            return parsed_results

        except Exception as e:
            self.logger.error(f"Error in agent discovery: {e}")
            return {
                "task_analysis": task_analysis,
                "error": str(e),
                "discovered_agents": [],
                "summary": f"Error during discovery: {str(e)}",
                "raw_discovery": ""
            }

    def _parse_discovery_result(self, discovery_result: str) -> Dict[str, Any]:
        """Parse the structured discovery result into a dictionary."""
        result = {
            "discovered_agents": [],
            "summary": ""
        }

        try:
            # Add debugging to see what we're actually getting
            self.logger.info(f"Raw discovery result to parse: {discovery_result}")
            
            lines = discovery_result.split('\n')
            current_section = None
            
            for line in lines:
                line = line.strip()
                if not line:  # Skip empty lines
                    continue
                
                # Look for section headers
                if line.startswith("DISCOVERED_AGENTS:"):
                    current_section = "agents"
                    continue
                elif line.startswith("SUMMARY:"):
                    current_section = "summary"
                    # Try to extract summary from the same line
                    summary_parts = line.split(":", 1)
                    if len(summary_parts) > 1:
                        result["summary"] = summary_parts[1].strip()
                    continue
                elif current_section == "summary" and line:
                    # Continue building summary if it spans multiple lines
                    if result["summary"]:
                        result["summary"] += " " + line
                    else:
                        result["summary"] = line
                    continue
                
                # Parse agent lines in the new format: "1. AGENT_ID: xyz | SCORE: 0.8 | REASON: ..."
                if current_section == "agents" and ("AGENT_ID:" in line and "SCORE:" in line):
                    try:
                        # Extract agent information using the new format
                        agent_info = self._extract_agent_info_from_formatted_line(line)
                        if agent_info:
                            # Find agent details from registry
                            agent_details = self._find_agent_by_id(agent_info["agent_id"])
                            
                            if agent_details:
                                result["discovered_agents"].append({
                                    "agent_id": agent_info["agent_id"],
                                    "agent_name": agent_details["name"],
                                    "score": agent_info["score"],
                                    "reason": agent_info["reason"],
                                    "specialization": agent_details["specialization"],
                                    "tools": agent_details["tools"],
                                    "workflows": agent_details["workflows"],
                                    "expertise": agent_details["expertise"]
                                })
                                self.logger.info(f"Successfully parsed agent: {agent_info['agent_id']} with score {agent_info['score']}")
                            else:
                                self.logger.warning(f"Agent ID '{agent_info['agent_id']}' not found in registry")
                        else:
                            self.logger.warning(f"Could not extract agent info from line: {line}")
                    except Exception as e:
                        self.logger.warning(f"Failed to parse agent line: {line} - {e}")
                
                # Also try to parse lines that contain scores or agent IDs even without strict format
                elif "score" in line.lower() and ("|" in line or ":" in line):
                    try:
                        agent_info = self._extract_agent_info_flexible(line)
                        if agent_info:
                            agent_details = self._find_agent_by_id(agent_info["agent_id"])
                            if agent_details:
                                result["discovered_agents"].append({
                                    "agent_id": agent_info["agent_id"],
                                    "agent_name": agent_details["name"],
                                    "score": agent_info["score"],
                                    "reason": agent_info["reason"],
                                    "specialization": agent_details["specialization"],
                                    "tools": agent_details["tools"],
                                    "workflows": agent_details["workflows"],
                                    "expertise": agent_details["expertise"]
                                })
                    except Exception as e:
                        continue

            # If we still found no agents, try a more aggressive parsing approach
            if not result["discovered_agents"]:
                self.logger.warning("No agents found with strict parsing, trying flexible approach...")
                result["discovered_agents"] = self._parse_discovery_flexible(discovery_result)

            self.logger.info(f"Final parsing result: found {len(result['discovered_agents'])} agents")

        except Exception as e:
            self.logger.error(f"Error parsing discovery result: {e}")

        return result

    def _extract_agent_info_from_formatted_line(self, line: str) -> Optional[Dict[str, Any]]:
        """Extract agent information from a formatted line."""
        try:
            # Initialize result
            agent_info = {
                "agent_id": "",
                "score": 0.0,
                "reason": ""
            }
            
            # Split by | first, then try other delimiters
            if "|" in line:
                parts = line.split("|")
            elif ";" in line:
                parts = line.split(";")
            else:
                parts = [line]
            
            for part in parts:
                part = part.strip()
                
                # Look for agent ID
                if ":" in part and ("agent" in part.lower() or any(agent["id"] in part for agent in self.agents_registry)):
                    # Try to extract agent ID
                    if ":" in part:
                        potential_id = part.split(":")[-1].strip()
                        # Check if this matches any agent ID in our registry
                        if any(agent["id"] == potential_id for agent in self.agents_registry):
                            agent_info["agent_id"] = potential_id
                    
                # Look for score
                if "score" in part.lower() and ":" in part:
                    try:
                        score_str = part.split(":")[-1].strip()
                        # Extract numeric value
                        import re
                        score_match = re.search(r'(\d+\.?\d*)', score_str)
                        if score_match:
                            agent_info["score"] = float(score_match.group(1))
                    except:
                        pass
                
                # Look for reason
                if "reason" in part.lower() and ":" in part:
                    agent_info["reason"] = part.split(":", 1)[-1].strip()
            
            # Also try to find agent IDs directly in the line
            if not agent_info["agent_id"]:
                for agent in self.agents_registry:
                    if agent["id"] in line:
                        agent_info["agent_id"] = agent["id"]
                        break
            
            # Return only if we found an agent ID
            return agent_info if agent_info["agent_id"] else None
            
        except Exception as e:
            self.logger.debug(f"Error in formatted parsing: {e}")
            return None

    def _extract_agent_info_flexible(self, line: str) -> Optional[Dict[str, Any]]:
        """Extract agent information from a line using flexible parsing."""
        try:
            # Initialize result
            agent_info = {
                "agent_id": "",
                "score": 0.0,
                "reason": ""
            }
            
            # Split by | first, then try other delimiters
            if "|" in line:
                parts = line.split("|")
            elif ";" in line:
                parts = line.split(";")
            else:
                parts = [line]
            
            for part in parts:
                part = part.strip()
                
                # Look for agent ID
                if ":" in part and ("agent" in part.lower() or any(agent["id"] in part for agent in self.agents_registry)):
                    # Try to extract agent ID
                    if ":" in part:
                        potential_id = part.split(":")[-1].strip()
                        # Check if this matches any agent ID in our registry
                        if any(agent["id"] == potential_id for agent in self.agents_registry):
                            agent_info["agent_id"] = potential_id
                    
                # Look for score
                if "score" in part.lower() and ":" in part:
                    try:
                        score_str = part.split(":")[-1].strip()
                        # Extract numeric value
                        import re
                        score_match = re.search(r'(\d+\.?\d*)', score_str)
                        if score_match:
                            agent_info["score"] = float(score_match.group(1))
                    except:
                        pass
                
                # Look for reason
                if "reason" in part.lower() and ":" in part:
                    agent_info["reason"] = part.split(":", 1)[-1].strip()
            
            # Also try to find agent IDs directly in the line
            if not agent_info["agent_id"]:
                for agent in self.agents_registry:
                    if agent["id"] in line:
                        agent_info["agent_id"] = agent["id"]
                        break
            
            # Return only if we found an agent ID
            return agent_info if agent_info["agent_id"] else None
            
        except Exception as e:
            self.logger.debug(f"Error in flexible parsing: {e}")
            return None

    def _parse_discovery_flexible(self, discovery_result: str) -> List[Dict[str, Any]]:
        """Last resort flexible parsing to find any agent mentions."""
        found_agents = []
        
        try:
            # Look for any agent IDs mentioned in the text
            for agent in self.agents_registry:
                if agent["id"] in discovery_result:
                    # Try to find a score near this agent ID
                    score = 0.5  # Default score
                    reason = "Agent mentioned in discovery result"
                    
                    # Look for numbers that might be scores near the agent ID
                    import re
                    agent_context = discovery_result.split(agent["id"])
                    if len(agent_context) > 1:
                        # Look in the text around the agent ID
                        context = (agent_context[0][-100:] + agent["id"] + agent_context[1][:100]).lower()
                        score_matches = re.findall(r'(\d+\.?\d*)', context)
                        for match in score_matches:
                            try:
                                potential_score = float(match)
                                if 0.0 <= potential_score <= 1.0:
                                    score = potential_score
                                    break
                                elif 0.0 <= potential_score <= 10.0:  # Could be out of 10
                                    score = potential_score / 10.0
                                    break
                            except:
                                continue
                    
                    found_agents.append({
                        "agent_id": agent["id"],
                        "agent_name": agent["name"],
                        "score": score,
                        "reason": reason,
                        "specialization": agent["specialization"],
                        "tools": agent["tools"],
                        "workflows": agent["workflows"],
                        "expertise": agent["expertise"]
                    })
                    
                    self.logger.info(f"Flexibly found agent: {agent['id']} with score {score}")
        
        except Exception as e:
            self.logger.error(f"Error in flexible discovery parsing: {e}")
        
        return found_agents

    def _find_agent_by_id(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """Find agent details by ID in the registry."""
        for agent in self.agents_registry:
            if agent["id"] == agent_id:
                return agent
        return None

    def _format_agents_for_discovery(self) -> str:
        """Format the agent registry for discovery prompt."""
        formatted_agents = []
        for agent in self.agents_registry:
            agent_info = f"""
- ID: {agent["id"]} | Name: {agent["name"]}
  Specialization: {agent["specialization"]}
  Expertise: {', '.join(agent["expertise"])}
  Keywords: {', '.join(agent["keywords"])}"""
            formatted_agents.append(agent_info)
        
        return '\n'.join(formatted_agents)

    def get_agent(self) -> AssistantAgent:
        """Get the underlying AssistantAgent instance for group chat."""
        if not self.agent:
            raise ValueError("Agent not initialized")
        return self.agent

    def is_initialized(self) -> bool:
        """Check if the agent is initialized."""
        return self.agent is not None

    @classmethod
    async def create_and_initialize(
        cls,
        model_name: Optional[str] = None,
        api_key: Optional[str] = None,
    ) -> "DiscoveryAgent":
        """
        Convenience method to create and initialize an agent in one call.
        """
        agent = cls(model_name=model_name, api_key=api_key)
        await agent.initialize()
        return agent 