"""
Orchestration Team Session Manager for handling orchestration team sessions with human-in-the-loop functionality.
"""

import asyncio
import logging
import uuid
from datetime import datetime
from typing import Any, AsyncGenerator, Dict, List, Optional, Union

from autogen_agentchat.messages import MultiModalMessage, TextMessage
from autogen_agentchat.teams import SelectorGroupChat
from autogen_core import CancellationToken

from ...helper.session_manager import Session<PERSON>anager
from ...kafka_client.producer import kafka_producer
from ...schemas.kafka import AgentChatResponse, HumanInputRequest, MessageAttachment
from ...shared.config.base import get_settings
from ...utils.file_processor import FileProcessor
from .orchestrator_agent import build_orchestration_team

logger = logging.getLogger(__name__)


class OrchestrationSessionManager:
    """
    Manages orchestration team sessions with human-in-the-loop functionality.
    """

    def __init__(self, session_manager: SessionManager):
        self.session_manager = session_manager
        self.settings = get_settings()
        self.file_processor = FileProcessor()
        self.active_orchestration_sessions: Dict[str, Dict[str, Any]] = {}
        self.human_input_pending: Dict[str, asyncio.Event] = {}
        self.human_input_responses: Dict[str, str] = {}
        self._lock = asyncio.Lock()

    async def create_orchestration_session(
        self,
        user_id: str,
        organization_id: Optional[str] = None,
        variables: Optional[Dict[str, Any]] = None,
    ) -> str:
        """
        Create a new orchestration team session.

        Args:
            user_id: ID of the user creating the session
            organization_id: Optional organization ID
            variables: Optional variables for the session

        Returns:
            Session ID for the orchestration team session
        """
        try:
            session_id = str(uuid.uuid4())

            # Build the orchestration team
            orchestration_team = await build_orchestration_team()

            # Create a dummy agent config for session manager compatibility
            dummy_agent_config = {
                "name": "orchestration_team",
                "description": "Global orchestration team with human-in-the-loop",
                "agent_type": "orchestration_team",
                "system_message": "Orchestration team session",
            }

            # Create session in the session manager
            await self.session_manager.create_session(
                agent_config=dummy_agent_config,
                user_id=user_id,
                communication_type="orchestration_team",
                organization_id=organization_id,
                use_knowledge=False,
                agent_group_id="orchestration_team",
                variables=variables,
            )

            # Store orchestration session data
            async with self._lock:
                self.active_orchestration_sessions[session_id] = {
                    "team": orchestration_team,
                    "user_id": user_id,
                    "organization_id": organization_id,
                    "variables": variables,
                    "created_at": datetime.utcnow().isoformat(),
                    "conversation_history": [],
                    "team_conversation_id": None,
                }

            logger.info(f"Created orchestration session: {session_id}")
            return session_id

        except Exception as e:
            logger.error(f"Failed to create orchestration session: {str(e)}")
            raise

    async def process_orchestration_chat(
        self,
        session_id: str,
        user_message: str,
        run_id: str,
        enable_human_input: bool = True,
        max_iterations: int = 10,
        attachments: Optional[List[MessageAttachment]] = None,
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Process a chat message with the orchestration team, including human-in-the-loop functionality.
        Supports both text and multimodal messages with attachments.

        Args:
            session_id: The session ID
            user_message: The user's message
            run_id: The run ID for this chat
            enable_human_input: Whether to enable human-in-the-loop
            max_iterations: Maximum number of team conversation rounds
            attachments: Optional list of file attachments (images, documents, etc.)

        Yields:
            Chat response chunks
        """
        try:
            async with self._lock:
                session_data = self.active_orchestration_sessions.get(session_id)

            if not session_data:
                yield {
                    "error": f"Orchestration session not found: {session_id}",
                    "success": False,
                    "final": True,
                }
                return

            team = session_data["team"]
            team_conversation_id = str(uuid.uuid4())

            # Update session with current conversation ID
            async with self._lock:
                self.active_orchestration_sessions[session_id][
                    "team_conversation_id"
                ] = team_conversation_id

            # Process attachments and create appropriate message
            attachment_summary = ""
            if attachments:
                # Validate attachments first
                validation_errors = self.file_processor.validate_attachments(
                    attachments
                )
                if validation_errors:
                    error_msg = "Attachment validation failed: " + "; ".join(
                        validation_errors
                    )
                    logger.error(error_msg)
                    yield {
                        "error": error_msg,
                        "success": False,
                        "final": True,
                    }
                    return

                attachment_summary = self.file_processor.get_attachment_summary(
                    attachments
                )
                logger.info(
                    f"Processing orchestration chat with attachments: {attachment_summary}"
                )

            # Add user message to conversation history
            user_msg_entry = {
                "role": "user",
                "content": user_message,
                "timestamp": datetime.utcnow().isoformat(),
                "attachments": (
                    [att.model_dump() for att in attachments] if attachments else []
                ),
                "attachment_summary": attachment_summary,
            }

            async with self._lock:
                self.active_orchestration_sessions[session_id][
                    "conversation_history"
                ].append(user_msg_entry)

            # Update session memory
            await self.session_manager.update_session_memory(
                session_id=session_id,
                message=user_msg_entry,
            )

            # Yield initial response
            yield {
                "run_id": run_id,
                "session_id": session_id,
                "message": "Starting orchestration team conversation...",
                "success": True,
                "final": False,
                "team_conversation_id": team_conversation_id,
            }

            # Create appropriate message for the team (text or multimodal)
            try:
                if attachments:
                    # Create multimodal message with attachments
                    team_message = await self.file_processor.create_multimodal_message(
                        user_message, attachments, source="user"
                    )
                    logger.info(
                        f"Created multimodal message for orchestration team with {attachment_summary}"
                    )
                else:
                    # Create simple text message
                    team_message = TextMessage(content=user_message, source="user")

            except Exception as e:
                logger.error(f"Failed to create message for orchestration team: {e}")
                yield {
                    "error": f"Failed to process attachments: {e}",
                    "success": False,
                    "final": True,
                }
                return

            # Process with orchestration team
            iteration_count = 0
            async for response in team.run_stream(task=team_message):
                iteration_count += 1

                if iteration_count > max_iterations:
                    yield {
                        "run_id": run_id,
                        "session_id": session_id,
                        "message": "Maximum team iterations reached",
                        "success": True,
                        "final": True,
                        "team_conversation_id": team_conversation_id,
                    }
                    break

                # Process team response
                response_content = getattr(response, "content", str(response))
                agent_name = getattr(response, "source", "team_member")

                # Add to conversation history
                team_msg_entry = {
                    "role": "assistant",
                    "content": response_content,
                    "agent": agent_name,
                    "timestamp": datetime.utcnow().isoformat(),
                }

                async with self._lock:
                    self.active_orchestration_sessions[session_id][
                        "conversation_history"
                    ].append(team_msg_entry)

                # Update session memory
                await self.session_manager.update_session_memory(
                    session_id=session_id,
                    message=team_msg_entry,
                )

                # Check if human input is needed and enabled
                if enable_human_input and self._should_request_human_input(
                    response_content
                ):
                    # Request human input
                    human_input = await self._request_human_input(
                        session_id=session_id,
                        run_id=run_id,
                        team_conversation_id=team_conversation_id,
                        prompt=f"The team needs your input. Recent response: {response_content[:200]}...",
                        context=self.active_orchestration_sessions[session_id][
                            "conversation_history"
                        ][-5:],
                        requesting_agent=agent_name,
                    )

                    if human_input:
                        # Add human input to conversation and continue
                        human_input_msg = TextMessage(
                            content=human_input, source="user"
                        )

                        # Add to conversation history
                        human_msg_entry = {
                            "role": "user",
                            "content": human_input,
                            "timestamp": datetime.utcnow().isoformat(),
                            "type": "human_input",
                        }

                        async with self._lock:
                            self.active_orchestration_sessions[session_id][
                                "conversation_history"
                            ].append(human_msg_entry)

                        # Update session memory
                        await self.session_manager.update_session_memory(
                            session_id=session_id,
                            message=human_msg_entry,
                        )

                        # Yield human input acknowledgment
                        yield {
                            "run_id": run_id,
                            "session_id": session_id,
                            "message": f"Human input received: {human_input}",
                            "success": True,
                            "final": False,
                            "team_conversation_id": team_conversation_id,
                            "human_input": human_input,
                        }

                # Yield team response
                yield {
                    "run_id": run_id,
                    "session_id": session_id,
                    "agent_response": {
                        "content": response_content,
                        "agent": agent_name,
                        "timestamp": team_msg_entry["timestamp"],
                    },
                    "success": True,
                    "final": False,
                    "team_conversation_id": team_conversation_id,
                }

                # Check if conversation should end
                if self._should_end_conversation(response_content):
                    yield {
                        "run_id": run_id,
                        "session_id": session_id,
                        "message": "Orchestration team conversation completed",
                        "success": True,
                        "final": True,
                        "team_conversation_id": team_conversation_id,
                    }
                    break

        except Exception as e:
            logger.error(f"Error in orchestration chat processing: {str(e)}")
            yield {
                "run_id": run_id,
                "session_id": session_id,
                "error": f"Error: {str(e)}",
                "success": False,
                "final": True,
            }

    def _should_request_human_input(self, response_content: str) -> bool:
        """
        Determine if human input should be requested based on response content.

        Args:
            response_content: The response content to analyze

        Returns:
            True if human input should be requested
        """
        # Simple heuristics - can be enhanced with more sophisticated logic
        human_input_triggers = [
            "need clarification",
            "please specify",
            "what would you prefer",
            "need more information",
            "unclear",
            "ambiguous",
            "which option",
            "your preference",
        ]

        content_lower = response_content.lower()
        return any(trigger in content_lower for trigger in human_input_triggers)

    def _should_end_conversation(self, response_content: str) -> bool:
        """
        Determine if the conversation should end based on response content.

        Args:
            response_content: The response content to analyze

        Returns:
            True if conversation should end
        """
        end_triggers = [
            "TERMINATE",
            "task completed",
            "conversation complete",
            "final answer",
            "conclusion",
        ]

        content_lower = response_content.lower()
        return any(trigger.lower() in content_lower for trigger in end_triggers)

    async def _request_human_input(
        self,
        session_id: str,
        run_id: str,
        team_conversation_id: str,
        prompt: str,
        context: List[Dict[str, Any]],
        requesting_agent: Optional[str] = None,
        timeout_seconds: int = 300,
    ) -> Optional[str]:
        """
        Request human input via Kafka and wait for response.

        Args:
            session_id: The session ID
            run_id: The run ID
            team_conversation_id: The team conversation ID
            prompt: The prompt for human input
            context: Recent conversation context
            requesting_agent: The agent requesting input
            timeout_seconds: Timeout for human response

        Returns:
            Human input string or None if timeout
        """
        try:
            # Create human input request
            input_request = HumanInputRequest(
                session_id=session_id,
                run_id=run_id,
                team_conversation_id=team_conversation_id,
                prompt=prompt,
                context=context,
                timeout_seconds=timeout_seconds,
                requesting_agent=requesting_agent,
            )

            # Create event for waiting
            input_event = asyncio.Event()
            self.human_input_pending[team_conversation_id] = input_event

            # Send request via Kafka
            await kafka_producer.send_message(
                topic=self.settings.kafka.kafka_human_input_request_topic,
                message=input_request.model_dump(),
                headers=[
                    ("session_id", session_id.encode("utf-8")),
                    ("run_id", run_id.encode("utf-8")),
                    ("team_conversation_id", team_conversation_id.encode("utf-8")),
                ],
            )

            # Wait for human input with timeout
            try:
                await asyncio.wait_for(input_event.wait(), timeout=timeout_seconds)

                # Get the response
                human_input = self.human_input_responses.get(team_conversation_id)

                # Clean up
                if team_conversation_id in self.human_input_pending:
                    del self.human_input_pending[team_conversation_id]
                if team_conversation_id in self.human_input_responses:
                    del self.human_input_responses[team_conversation_id]

                return human_input

            except asyncio.TimeoutError:
                logger.warning(
                    f"Human input timeout for conversation: {team_conversation_id}"
                )

                # Clean up
                if team_conversation_id in self.human_input_pending:
                    del self.human_input_pending[team_conversation_id]

                return None

        except Exception as e:
            logger.error(f"Error requesting human input: {str(e)}")
            return None

    async def handle_human_input_response(
        self,
        team_conversation_id: str,
        user_input: str,
    ) -> bool:
        """
        Handle human input response.

        Args:
            team_conversation_id: The team conversation ID
            user_input: The human input

        Returns:
            True if handled successfully
        """
        try:
            if team_conversation_id in self.human_input_pending:
                # Store the response
                self.human_input_responses[team_conversation_id] = user_input

                # Signal the waiting coroutine
                self.human_input_pending[team_conversation_id].set()

                logger.info(
                    f"Human input received for conversation: {team_conversation_id}"
                )
                return True
            else:
                logger.warning(
                    f"No pending input request for conversation: {team_conversation_id}"
                )
                return False

        except Exception as e:
            logger.error(f"Error handling human input response: {str(e)}")
            return False

    async def get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        Get orchestration session information.

        Args:
            session_id: The session ID

        Returns:
            Session information or None if not found
        """
        async with self._lock:
            return self.active_orchestration_sessions.get(session_id)

    async def cleanup_session(self, session_id: str) -> bool:
        """
        Clean up orchestration session.

        Args:
            session_id: The session ID to clean up

        Returns:
            True if cleaned up successfully
        """
        try:
            async with self._lock:
                if session_id in self.active_orchestration_sessions:
                    del self.active_orchestration_sessions[session_id]

            # Also clean up in the base session manager
            await self.session_manager.delete_session(session_id)

            logger.info(f"Cleaned up orchestration session: {session_id}")
            return True

        except Exception as e:
            logger.error(f"Error cleaning up orchestration session: {str(e)}")
            return False
